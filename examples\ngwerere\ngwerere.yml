video:
    start_frame: 0
    end_frame: 20
    h_a: 0.0
frames:
  normalize:
  edge_detect:
    wdw_1: 1
    wdw_2: 2
  minmax:
    min: -5
    max: 5

velocimetry:
  get_piv:
      window_size: 25
  write: True

mask:
  write: True
  mask_group1:
    corr:
  mask_group1_2:
    minmax:
  mask_group1_3:
    rolling:
  mask_group2:
    outliers:
  mask_group3:
    variance:
  mask_group_4:
      count:
  mask_group5:
    window_mean:
      wdw: 2
      tolerance: 0.5
      reduce_time: True

transect:
  write: True
  transect_1:
    shapefile: ../examples/ngwerere/cross_section1.geojson
    get_transect:
      rolling: 4
      wdw: 2
    get_q:
      fill_method: log_interp
      v_corr: 0.85
    get_river_flow:

  transect_2:
    shapefile: ../examples/ngwerere/cross_section2.geojson
    get_transect:
      rolling: 4
      wdw: 2
    get_q:
      fill_method: log_interp
      v_corr: 0.85
    get_river_flow:

plot:
  plot_quiver:
    frames:
    velocimetry:
      alpha: 0.4
      cmap: rainbow
      scale: 400
      width: 0.0015
      vmax: 0.6
    transect:
      transect_1:
        cmap: rainbow
        scale: 200
        width: 0.003
        vmin: 0
        vmax: 0.6
      transect_2:
        cmap: rainbow
        add_colorbar: True
        scale: 200
        width: 0.003
        vmin: 0
        vmax: 0.6

    mode: camera
    reducer: mean

    write_pars:
      dpi: 100
      bbox_inches: tight

