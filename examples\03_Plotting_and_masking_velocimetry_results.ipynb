{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["## Immersive plotting and analyzing results\n", "\n", "Lets do some analysis and plotting"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import xarray as xr\n", "import pyorc\n", "from matplotlib.colors import Normalize\n"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["You have a result stored in a NetCDF file after running notebook 02. Now you want to see if the results seem to make sense, and further analyze these. Especially post-processing of your results with masking invalid velocities is an essential step in most velocimetry analyses, given that sometimes hardly any tracers are visible, and only spuriously correlated results were found. With some intelligence these suspicious velocities can be removed, and `pyo<PERSON><PERSON> has many methods available to do that. Most of these can be applied without any parameter changes for a good result. The order in which mask methods are applied can matter, which we will describe later in this notebook.\n", "\n", "Let's first have a look at the file structure. We can simply use the `xarray` API to open the result file."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["ds = xr.open_dataset(\"ngwerere/ngwerere_piv.nc\")\n"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["As you can see, we have lots of coordinate variables at our disposal, these can be used in turn to plot our data in a local projection, with our bounding box top-left corner at the top-left. The `x` and `y` axes hold the local coordinates. We can also use the UTM35S coordinates, stored in `xs` and `ys`, the longitude and latitude coordinates stored in `lon` and `lat`, or....(very cool) the original row and column coordinate of the camera's objective. This allows us to plot the results as an augmented reality view.\n", "\n", "In the data variables, we see `v_x` and `v_y` for the x-directional and y-directional velocity, measured along the `x` and `y` axis of the local coordinate system. Furthermore we see the `s2n` variable containing the signal to noise ratio computed by the underlying used library OpenPIV. This ratio is computed as the ratio between the highest correlation and the second highest correlation found in the surroundings of each window. If this ratio is very low it means that it is difficult to assess which correlation value is the best. We also store the highest correlation found in the variable `corr`. As this is done for each frame to frame pair, the dataset contains 125 time steps, since we extract 126 frames.\n", "\n", "Because we used a 0.01 resolution and a 25 pixel interrogation window, and `pyorc` typically uses an overlap between windows of 50%, the `y` and `x` coordinates have a spacing of `25 * 0.01 * 0.5 = 0.125 cm` (rounded to 0.13).\n", "\n", "Finally, a lot of attributes are stored in a serializable form, so that the dataset can be stored in NetCDF, and the camera configuration and other information needed can be used by our postprocessing and plotting methods after loading. Normally, you don't have to worry about all these details and simply use the methods in `pyorc` to deal with them.\n"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["### Plotting in local projection\n", "Both a frames `DataArray` and a velocimetry `Dataset` have plotting functionalities that can be used to combine information into a plot. The default is to plot data in the local projection that follows the area of interest bounding box, but geographical plots or camera perspectives can also be plotted. Let's start with an rgb frame with the PIV results on top. We cannot plot the time dimension, so we apply a `mean` reducer first. To prevent that all metadata of the variables gets lost in this process, the `keep_attrs=True` flag is applied.\n", "\n", "Note that whilst plotting velocimetry results, we can supply `kwargs_scalar` and `kwargs_quiver`. These are meant to pass arguments to plotting of the scalar velocity values (plotted as a mesh) and the vectors (plotted as arrows)."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# first re-open the original video, extract one RGB frame and plot that\n", "video_file = \"ngwerere/ngwerere_20191103.mp4\"\n", "\n", "video = pyorc.Video(video_file, start_frame=0, end_frame=125)\n", "# borrow the camera config from the velocimetry results\n", "video.camera_config = ds.velocimetry.camera_config\n", "\n", "da_rgb = video.get_frames(method=\"rgb\")\n", "# project the rgb frame\n", "da_rgb_proj = da_rgb.frames.project()\n", "# plot the first frame (we only have one) without any arguments, default is to use \"local\" mode\n", "p = da_rgb_proj[0].frames.plot()\n", "\n", "# now plot the results on top, we use the mean, because we cannot plot more than 2 dimensions. \n", "# Default plotting method is \"quiver\", but \"scatter\" or \"pcolormesh\" is also possible.\n", "# We add a nice colorbar to understand the magnitudes.\n", "# We give the existing axis handle of the mappable returned from .frames.plot to plot on, and use \n", "# some transparency.\n", "ds_mean = ds.mean(dim=\"time\", keep_attrs=True)\n", "\n", "# first a pcolormesh\n", "ds_mean.velocimetry.plot.pcolormesh(\n", "    ax=p.axes,\n", "    alpha=0.3,\n", "    cmap=\"rainbow\",\n", "    add_colorbar=True,\n", "    vmax=0.6\n", ")\n", "\n", "ds_mean.velocimetry.plot(\n", "    ax=p.axes,\n", "    color=\"w\",\n", "    alpha=0.5,\n", ")\n"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["### Masking of results\n", "Already this looks very promising. But we haven't yet analyzed any of the velocities for spurious values. Even over land we have estimates of velocities, while there is no moving water there. We have a set of temporal masking methods which analyze for spurious values by comparing over time steps, and spatial masking methods, which compare neighbouring grid cell values to flag spurious values. These are defined under a subclass in ``ds.velocimetry.mask``. \n", "\n", "The mask methods all return a xr.DataArray with the same size as the DataArray variables in the dataset and can be applied in two ways:\n", "\n", "- first defining several masks, without applying these on the dataset, and then collectively apply them. This is the default behaviour. Masks can be collectively applied with ``ds.velocimetry.mask([mask1, mask2, mask3, ...])``.\n", "- apply the mask immediately on the dataset (use ``inplace=True`` on each mask method).\n", "\n", "In the last case, you should be aware that applying another mask method after having applied other mask methods makes the results conditional on masks already applied. Therefore in this case the order in which you apply masks will change the results. For instance, if you first mask out velocities that are outside a minimum / maximum velocity range using ``ds.velocimetry.mask.minmax``, and only after that mask out based on variance in time (points with a high variance can be masked), the computed variance over time for each location will likely already be lower, because you may have excluded outlier velocities. The method ``ds.velocimetrry.mask.count`` simply counts how many values are found per grid cell, relative to the total amount of time steps available. This method typically requires that you first apply a set of other mask methods, before being applied.\n", "\n", "Below, we decided to apply masks in a certain order, and conditional on each other. We start with masks that can typically be applied on individual values, such as the ``minmax`` and ``corr``. And then gradually move to masks that require analysis of all values in time, such as the ``variance`` method and (see above) the ``count`` method. This is typically the good practice.\n"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["import copy\n", "ds_mask = copy.deepcopy(ds)\n", "mask_corr = ds_mask.velocimetry.mask.corr(inplace=True)\n", "mask_minmax = ds_mask.velocimetry.mask.minmax(inplace=True)\n", "mask_rolling = ds_mask.velocimetry.mask.rolling(inplace=True)\n", "mask_outliers = ds_mask.velocimetry.mask.outliers(inplace=True)\n", "mask_var = ds_mask.velocimetry.mask.variance(inplace=True)\n", "mask_angle = ds_mask.velocimetry.mask.angle(inplace=True)\n", "mask_count = ds_mask.velocimetry.mask.count(inplace=True)\n", "\n", "\n", "# apply the plot again, let's leave out the scalar values, and make the quivers a bit nicer than before.\n", "ds_mean_mask = ds_mask.mean(dim=\"time\", keep_attrs=True)\n", "\n", "\n", "# again the rgb frame first\n", "p = da_rgb_proj[0].frames.plot()\n", "\n", "#...and then masked velocimetry\n", "ds_mean_mask.velocimetry.plot(\n", "    ax=p.axes,\n", "    alpha=0.4,\n", "    norm=Normalize(vmax=0.6, clip=False),\n", "    add_colorbar=True\n", ")\n"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Interesting! We see that the velocities become a lot higher on average. Most likely because many spurious velocities are removed. We also see that the velocities seem to be more left-to-right oriented. In part this may be because we applied the `.mask.angle` method. This mask removes velocities that are in a direction, far off from the expected flow direction. The default direction of this mask is always left to right. Therefore take good care of this mask, if you decide to e.g. apply `pyorc` in a bottom to top direction.\n", "\n", "As this stream is very shallow and has lots of obstructions like rocks that the water goes around, the application of this mask may have removed many velocities that were actually correct. Let's do the masking again, but relaxing the tolerance of the angle mask.\n", "\n", "Besides this, we can also apply a number of spatial masks. It is often logical to apply these on the mean of the dataset in time. This can be done by adding the ``reduce_time`` flag. Note that ``reduce_time`` can in principle also be used on any other mask that does not per se require ``time`` as a dimension. The spatial mask we demonstrate here compares the value under consideration against a window mean around that location. If the window mean (after reducing the ``time`` dimension with a mean) is very different, the value at that location will be excluded.\n"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# apply all methods in time domain with relaxed angle masking\n", "import numpy as np\n", "ds_mask2 = copy.deepcopy(ds)\n", "ds_mask2.velocimetry.mask.corr(inplace=True)\n", "ds_mask2.velocimetry.mask.minmax(inplace=True)\n", "ds_mask2.velocimetry.mask.rolling(inplace=True)\n", "ds_mask2.velocimetry.mask.outliers(inplace=True)\n", "ds_mask2.velocimetry.mask.variance(inplace=True)\n", "ds_mask2.velocimetry.mask.angle(angle_tolerance=0.5*np.pi)\n", "ds_mask2.velocimetry.mask.count(inplace=True)\n", "ds_mask2.velocimetry.mask.window_mean(wdw=2, inplace=True, tolerance=0.5, reduce_time=True)\n", "\n", "# Now first average in time before applying any filter that only works in space.\n", "ds_mean_mask2 = ds_mask2.mean(dim=\"time\", keep_attrs=True)\n", "\n", "# apply the plot again\n", "# again the rgb frame first\n", "p = da_rgb_proj[0].frames.plot()\n", "\n", "#...and then filtered velocimetry\n", "ds_mean_mask2.velocimetry.plot(\n", "    ax=p.axes,\n", "    alpha=0.4,\n", "    norm=Normalize(vmax=0.6, clip=False),\n", "    add_colorbar=True\n", ")\n"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["It looks more natural. Check for instance the pattern around the rock on the left side. Now we can also plot in a geographical view."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# again the rgb frame first. But now we use the \"geographical\" mode to plot on a map\n", "p = da_rgb_proj[0].frames.plot(mode=\"geographical\")\n", "\n", "#...and then masked velocimetry again, but also geographical\n", "ds_mean_mask2.velocimetry.plot(\n", "    ax=p.axes,\n", "    mode=\"geographical\",\n", "    alpha=0.4,\n", "    norm=Normalize(vmax=0.6, clip=False),\n", "    add_colorbar=True\n", ")\n", "\n", "# for fun, let's also add a satellite background from cartopy\n", "import cartopy.io.img_tiles as cimgt\n", "import cartopy.crs as ccrs\n", "tiles = cimgt.GoogleTiles(style=\"satellite\")\n", "p.axes.add_image(tiles, 19)\n", "# zoom out a little bit so that we can actually see a bit\n", "p.axes.set_extent([\n", "    da_rgb_proj.lon.min() - 0.00005,\n", "    da_rgb_proj.lon.max() + 0.00005,\n", "    da_rgb_proj.lat.min() - 0.00005,\n", "    da_rgb_proj.lat.max() + 0.00005],\n", "    crs=ccrs.<PERSON><PERSON>()\n", ")\n"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["### Immersive and intuitive augmented reality\n", "And now the most beautiful plot you can make with pyorc: an augmented reality view. For this, we need an unprojected rgb frame and supply the `mode=\"camera\"` keyword argument."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# again the rgb frame first, but now the unprojected one. Now we use the \"camera\" mode to plot the camera perspective\n", "p = da_rgb[0].frames.plot(mode=\"camera\")\n", "\n", "#...and then masked velocimetry again, but also camera. This gives us an augmented reality view. The quiver scale \n", "# needs to be adapted to fit in the screen properly\n", "ds_mean_mask.velocimetry.plot(\n", "    ax=p.axes,\n", "    mode=\"camera\",\n", "    alpha=0.4,\n", "    norm=Normalize(vmin=0., vmax=0.6, clip=False),\n", "    add_colorbar=True\n", ")\n"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["### Store the final masked results\n", "Let's also store the masked velocities in a separate file. To ensure it remains really small, we can use the `.set_encoding` method first, to ensure the variables are encoded to integer values before storing. This makes the file nice and small."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["ds_mask2.velocimetry.set_encoding()\n", "ds_mask2.to_netcdf(\"ngwerere_masked.nc\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}