.. _intro:

============
Introduction
============

.. _nutshell:

pyOpenRiverCam in a nutshell
============================

pyOpenRiverCam is a Command-line interface and Application Programming Interface (API) to preprocess, reproject, and
analyze videos of rivers, in order to estimate river flows. Below we provide an overview of all functionalities in a
nutshell:

+----------------------------------+-----------------------------------------------------------------------------------+
| Feature                          | Example                                                                           |
+==================================+===================================================================================+
| Create geographical awareness    | .. image:: _images/wark_cam_config.jpg                                            |
| of your videos using your own    |                                                                                   |
| field observations               |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Work with, and pre-process       | .. image:: _images/video_orig.gif                                                 |
| video frames                     |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Enhance frames to improve        | .. image:: _images/video_norm.gif                                                 |
| visibility of tracers            |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Reproject frames to metres-      | .. image:: _images/video_norm_proj.gif                                            |
| distance planar views            |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Enhance gradients for improved   | .. image:: _images/video_edge.gif                                                 |
| feature detection                |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Estimate flow velocity at the    | .. image:: _images/wark_streamplot.jpg                                            |
| water surface using Particle     |                                                                                   |
| Image Velocimetry.               |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Estimate river discharge over    | .. image:: _images/wark_discharge.jpg                                             |
| a supplied cross-section.        |                                                                                   |
| Use smart functions to fill      |                                                                                   |
| missing data.                    |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
| Plot combined views in immersive | .. image:: _images/wark_cam_persp.jpg                                             |
| local, geographical or camera    |                                                                                   |
| perspectives.                    |                                                                                   |
+----------------------------------+-----------------------------------------------------------------------------------+
