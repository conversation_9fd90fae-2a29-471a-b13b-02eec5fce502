{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["## Camera calibration\n", "**pyorc** is equipped with a very straightforward manner to calibrate your camera lens. The process is as follows:\n", "\n", "* Take a video with a chessboard pattern ensuring you maximize difference in location in the FOV where the chessboard is shown, and the different rotation angles under which the chessboard is shown. Ensure that the video is taken with the *exact same settings* as you expect to use in the field.\n", "* Start with a fresh camera configuration, ensuring height and width follow the settings of your intended video resolution\n", "* Feed in the calibration video. This will extract a set amount of frames with recognised chessboard patterns widely sampled over the entire video, calibrate the intrinsic matrix and distortion coefficients, and overwrite these in the camera configuration.\n", "\n", "In this notebook we will demonstrate this entire process. It should be noted that if your lens only has very little distortion, this process is likely not needed and may in fact even deteriorate your results."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import copy\n", "import cv2\n", "import glob\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import os\n", "import pyorc"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["We pretend that we are going to treat videos of 720p resolution (height: 720, width: 1280). A sample video is included in the repository. Let's first have a look at the first frame of this video."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["fn = \"camera_calib/camera_calib_720p.mkv\"\n", "vid = pyorc.Video(fn)\n", "frame = vid.get_frame(0, method=\"rgb\")\n", "plt.imshow(frame)"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["The algorithm uses automatic detection of chessboard corners. It only detects chessboard corners *within* the image, so not at the edge. Hence, this is a 9x6 pattern, which is the default setting. Let's now first make a default camera configuration, without the camera calibration applied."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["cam_config = pyorc.CameraConfig(height=720, width=1280)\n", "cam_config\n"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["### using the wrong chessboard pattern\n", "You can see the camera matrix above looks quite like a default, and distortion coefficients are all zero. Let's now do a calibration. Let us first have a look what happens if the wrong chessboard pattern is passed. We assume the user thought that the outer edges should also be included. That would mean the user would apply a 11x8 pattern. To ensure we are not searching for a very long time, we limit the amount of frames to 50."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["cam_config.set_lens_calibration(fn, chessboard_size=(11, 8), frame_limit=50)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["That gave an error, let's now do it the right way. We use the right chessboard size. While executing this, you may comment the first line (setting `plot=True`) and uncomment the second to see a regularly updated pop up with visual feedback. In the process we also set `to_file=True`. This causes the algorithm to also write .png files of the found frames and the found corner points. These files are always found side-by-side with the provided video. "]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["cam_config.set_lens_calibration(fn, chessboard_size=(9, 6), plot=False, to_file=True)\n", "# cam_config.set_lens_calibration(fn, chessboard_size=(9, 6), plot=True, to_file=True)"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["So what happened? The algorithm siffs through all frames in such a way that all parts of the video are very well covered (i.e. not in the order in which the frames are stored). It start with the first and last frame. Then samples the frame in the middle. Then in between the first and middle, and middle and last, and so forth. In this way we ensure that all poses shown are well sampled and that we do not end up with many samples that show almost the same pose, causing overfitting of the parameters on just one pose (which may in fact give very very bad results, rendering camera calibration a bad thing rather than a good thing. The algorithm stops searching for corner points once `max_frames` is reached, which defaults to 30. The progress bar therefore does not reach 100%, because that would mean all frames in the video are read. The error is a measure for the average pixel error made when projecting the found corner points back to the image using the calibrated parameters. Let's check what the intrinsic matrix and distortion coefficients look like."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["print(f\"Camera Matrix: {cam_config.camera_matrix}\")\n", "print(f\"Distortion coefficients: {cam_config.dist_coeffs}\")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["They are now clearly different from the default. We can also plot the images, written to disk to verify if we are happy with the found coverage of poses. If a large part of the VOF or rotations is missing, we can still extend the amount of frames"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["paths = glob.glob(os.path.join(\"camera_calib\", \"*.png\"))\n", "\n", "# plot all results in 3 columns\n", "cols = 3\n", "rows = int(np.ceil(len(paths)/cols))\n", "rows, cols\n", "f = plt.figure(figsize=(16, 3*rows))\n", "for n, fn in enumerate(paths):\n", "    ax = plt.subplot(rows, cols, n + 1)\n", "    img = cv2.imread(fn)\n", "    # switch colors\n", "    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "    ax.imshow(img)\n", "    ax.tick_params(\n", "        left=False,\n", "        right=False,\n", "        labelleft=False,\n", "        labelbottom=False,\n", "        bottom=False,\n", "    )\n"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["### Result of undistortion\n", "Below we will have a look what the undistortion process does with the resulting frames. The plot of the difference reveals that in the middle, distortions were very small, while at the edges, distortions are larger. The image is somewhat stretched at the edges. This is nicely corrected for with our calibration. All extracted frames and the `src` point locations are automatically undistorted while transforming a video into frames and performing orthorectifaction."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["import cv2\n", "from pyorc.cv import undistort_img  # helper function to undistort images with camera_matrix and dist_coeffs\n", "import matplotlib.pyplot as plt\n", "fn = \"camera_calib/camera_calib_720p.mkv\"\n", "\n", "# open without camera configuration\n", "vid = pyorc.Video(fn)\n", "frame = vid.get_frame(0, method=\"rgb\")\n", "\n", "# undistort frame with the found camera matrix and distortion coefficients\n", "frame_undistort = undistort_img(\n", "    frame,\n", "    camera_matrix=cam_config.camera_matrix,\n", "    dist_coeffs=cam_config.dist_coeffs\n", ")\n", "diff = np.mean(np.int16(frame) - np.int16(frame_undistort), axis=-1)\n", "\n", "f = plt.figure(figsize=(16, 16))\n", "ax1 = plt.axes([0.05, 0.45, 0.3, 0.2])\n", "ax2 = plt.axes([0.45, 0.45, 0.3, 0.2])\n", "ax3 = plt.axes([0.1, 0.05, 0.6, 0.4])\n", "cax = plt.axes([0.75, 0.1, 0.01, 0.2])\n", "ax1.set_title(\"Original\")\n", "ax2.set_title(\"Undistorted\")\n", "ax3.set_title(\"Difference\")\n", "ax1.imshow(frame)\n", "ax2.imshow(frame_undistort)\n", "\n", "# make some modern art for the difference\n", "p = ax3.imshow(diff, cmap=\"RdBu\", vmin=-100, vmax=100)\n", "plt.colorbar(p, cax=cax, extend=\"both\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}