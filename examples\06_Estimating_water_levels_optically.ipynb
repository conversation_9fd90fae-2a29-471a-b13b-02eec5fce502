{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["## Estimating a water level optically using the camera configuration and a cross section\n", "\n", "If you do not have the means to measure a water level from incoming new videos of a scene, but you do have a fully configured camera configuration and a *in situ* measured cross section, you may assess the water level by automatic optical inference of the water level along the cross section. In this notebook we demonstrate how you can use these methods.\n", "\n", "Before you dive into this notebook, we recommend to first look at **notebook 01**, how to establish a camera configuration."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import os\n", "import xarray as xr\n", "import pandas as pd\n", "import geopandas as gpd\n", "import pyorc\n", "import cartopy.crs as ccrs\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import Normalize"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["The starting point for measuring a water level in an incoming video is an existing camera configuration, which provides the information on the camera's perspective. \n", "\n", "Also, we require a cross section. The way the water level measurement approach works is roughly as follows:\n", "\n", "- the algorithm hypothesizes a randomly chosen possible location in the cross section as being the location where the cross section is exactly on the edge of the water surface.\n", "- At that location, the algorithm draws a line, perpendicular to the cross-section as a hypothesis of being the water line.\n", "- The algorithm then creates horizontally oriented rectangles, left and right of the hypothesized water line, projects these to the image, and samples intensities from a derived image of the incoming video.\n", "- The intensity distributions of both rectangles are compared. If the distributions are very different it is more likely that the drawn line indeed represented the water line, if they are quite similar it is less likely.\n", "- The algorithm uses a search method to repeatedly do this analysis and find the location where the intensity distribution function are the most dissimilar. The vertical level belonging to this cross-section location is retrieved and returned to the user.\n", "\n", "Below we show what the cross section object contains, and show how it can be used to estimate the water level.\n", "\n", "First we load in a cross section, a video, and a camera configuration. Note:\n", "- we use here the same video as before, but you can use any video with any water level as long as it belongs to the same camera configuration and the cross section reaches far enough into the banks to be able to detect it crossing the water surface.\n", "- we only use a single frame from the video as we don't need to interpret movements."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["cam_config = pyorc.load_camera_config(\"ngwerere/ngwerere.json\")\n", "# cs_file = \"ngwerere_cross_section_new.csv\"\n", "cs_file = \"ngwerere/cross_section2.geojson\"\n", "video_file = \"ngwerere/ngwerere_20191103.mp4\"\n", "\n", "# we load the video as before\n", "video = pyorc.Video(\n", "    video_file,\n", "    camera_config=cam_config,\n", "    start_frame=0,\n", "    end_frame=1,\n", "    h_a=0.,\n", ")\n", "\n", "# the cross section is stored in a simple geojson point (x, y, z) file\n", "cs_gdf = gpd.read_file(cs_file)\n", "cross_section = pyorc.CrossSection(camera_config=cam_config, cross_section=cs_gdf)\n", "cross_section"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["We see a linestring representation of the cross section points. You can clearly see that the linestring contains our x, y and z real-world coordinates. But we want to see a more clear picture. Let's plot this in 3D"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["ax = cross_section.plot()\n"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Interesting plot, but it would also be good to see what it looks like from the camera perspective. Let's plot both in 2 different subplots. The first subplot is a simple 2D axes with a RGB frame from our video. But we add an augmented reality of the cross section with `camera=True`. We can control what we exactly want to see with the `bottom`, `planar` and `wetted` flags. We can also control the plot keyword arguments like line width, alpha level, and so on for all through `bottom_kwargs`, `planar_kwargs` and `wetted_kwargs`We also add the camera configuration to both."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["fig = plt.figure(figsize=(16, 7))\n", "ax1 = fig.add_subplot(1, 2, 1)\n", "# for the 3D plot, we need to explicitly define a 3d projection\n", "ax2 = fig.add_subplot(1, 2, 2, projection=\"3d\")\n", "# now plot an image on the first \n", "frame = video.get_frames(method=\"rgb\")[0]\n", "ax1.imshow(frame)  # .frames.plot(ax=ax1)\n", "# on top of this, plot the cross section WITH camera=True, we skip the planar surface and make the bottom look a bit stronger.\n", "cross_section.plot(camera=True, ax=ax1, planar=False, bottom_kwargs={\"alpha\": 0.6, \"color\": \"brown\"})\n", "# and add the camera config too!\n", "cross_section.camera_config.plot(mode=\"camera\", ax=ax1)\n", "\n", "# and do the 3d plot also\n", "cross_section.plot(ax=ax2)\n", "cross_section.camera_config.plot(mode=\"3d\", ax=ax2)\n", "ax2.set_aspect(\"equal\")"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Now we have a slightly better impression of the situation at the site and how our cross section fits with the camera configuration. Remember from the first notebook, that as this video is only one snapshot, we have set `h_ref` to 0.0 meters. This means that our optical measurement method should give us about 0.0 meters as water level. Let's exxtract one grayscale image and see how this goes."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["img = video.get_frames()[0].values  # we use \"values\" as we need a raw numpy array\n", "h = cross_section.detect_water_level(img)\n", "print(f\"The optimized water level is {h} meters\")\n"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["You should see something near 0.055 meters. As this is from an optmization algorithm the exact value may vary a little bit. Not bad at all as this is only about 5cm from the known water level! We can now also make a plot, that shows the wetted cross section at this level"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["fig = plt.figure(figsize=(12, 7))\n", "ax = fig.add_subplot(1, 1, 1)\n", "# plot the rgb image\n", "ax.imshow(frame)  # .frames.plot(ax=ax1)\n", "# on top of this, plot our new water level, with also the water line, using 2 plot methods\n", "cross_section.plot(camera=True, ax=ax, planar=False, bottom_kwargs={\"alpha\": 0.4, \"color\": \"brown\"})\n", "cross_section.plot_water_level(h=h, camera=True, ax=ax)\n", "# and add the camera config too!\n", "cross_section.camera_config.plot(mode=\"camera\", ax=ax1)\n", "\n", "# and do the 3d plot also\n", "cross_section.plot(ax=ax2)\n", "cross_section.camera_config.plot(mode=\"3d\", ax=ax2)\n", "ax2.set_aspect(\"equal\")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Note that the bank here, is quite clearly defined with the much more bright-colored grass. The grass is also not too short. Let's also have a look at the caveats here. We have another example video on https://zenodo.org/records/********.\n", "\n", "We know that during this event the water level was **93.345 meter** in the local datum."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["vid_file = pyorc.sample_data.get_hommerich_dataset()\n", "vid_path = pyorc.sample_data.get_hommerich_pyorc_files()\n", "cam_config_file = os.path.join(vid_path, \"cam_config_gcp1.json\")\n", "cross_section_fn = os.path.join(vid_path, \"cs1_ordered.geojson\")\n", "\n", "# get a new video object\n", "cam_config = pyorc.load_camera_config(cam_config_file)\n", "video = pyorc.Video(vid_file, camera_config=cam_config)\n", "\n", "# get a new cross section object\n", "gdf = gpd.read_file(cross_section_fn)\n", "cross_section = pyorc.CrossSection(camera_config=cam_config, cross_section=gdf)\n", "\n", "# plot together\n", "fig = plt.figure(figsize=(16, 7))\n", "ax1 = fig.add_subplot(1, 2, 1)\n", "ax2 = fig.add_subplot(1, 2, 2, projection=\"3d\")\n", "frame = video.get_frames(method=\"rgb\")[0]\n", "ax1.imshow(frame)\n", "# we plot with the actual water level during the video now.\n", "cross_section.plot(camera=True, h=93.345, ax=ax1)\n", "cross_section.plot_water_level(camera=True, h=93.345, ax=ax1)\n", "\n", "cross_section.plot(ax=ax2)\n", "cross_section.camera_config.plot(mode=\"3d\", ax=ax2)\n", "ax2.set_aspect(\"equal\")\n", "# switch off axis labelling\n", "ax1.set_axis_off()"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Ok, we have a new dataset, with a video taken during a high flow event. It can clearly be seen that the real wetted cross section (see left) is partly obscured by vegetation that grows over the water in the direction of the camera position. This in fact happens at both sides of the stream. That might become problematic. Let's see what happens if we extract a grayscale image, and try our best luck."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["img = video.get_frames()[0].values\n", "h = cross_section.detect_water_level(img)\n", "print(f\"The optimized water level is {h} meters\")\n", "\n", "# and also make a plot\n", "fig = plt.figure(figsize=(16, 7))\n", "ax = fig.add_subplot(1, 1, 1)\n", "ax.imshow(frame)\n", "# we plot with the actual water level during the video now.\n", "cross_section.plot(camera=True, h=h, ax=ax)\n", "cross_section.plot_water_level(camera=True, h=h, ax=ax)\n", "ax.set_axis_off()\n"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["\n", "Indeed you can see that the water level is guessed to be more or less on the edge of the vegetation - water line resulting in a (much) lower water level. The land - water line is somewhere hidden under the foliage. The message is **use common sense if the situation allows for water level detection with this method**. Known circumstances where things may not work are:\n", "* dark sand looking like water\n", "* shaded areas\n", "* non-moving water with mirroring reflections\n", "* overhanging vegetation, growing towards the camera\n", "\n", "And on the happy side, situations where this may work very well:\n", "\n", "* if you have one or several staff gauges in view: simply draw a couple of connected points from the bottom to top of the first staff gauge, then moving horizontally to the second higher one, then the third, and so on. Just ensure that the `length` parameter is set smaller, approximately to the width of the staff gauge, and you'll have a very good contrasting cross section.\n", "* if you have a concrete straight bridge pier or concrete channel or embankment. Easy to measure a cross section!\n", "* arid areas with flash floods in sandy streams. Fast flowing water will have serious contrast against the much lighter sand."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}