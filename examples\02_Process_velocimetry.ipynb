{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["## Analyze surface velocities of a video with velocimetry\n", "This notebook shows how to use a camera configuration, and a video to estimate velocities at the surface. \n", "It also demonstrates the important impact of filtering of spurious or noisy velocities on the end result. We go through the following steps:\n", "\n", "* Read a pre-defined camera configuration from file (we use the one prepared in notebook 1)\n", "* Open a video, and provide the predefined camera configuration to it.\n", "* Project frames to a planar projection\n", "* Estimate surface velocities with Particle Image Velocimetry\n", "* Filter raw velocities using several temporal and spatial filters\n", "* Plot results in the camera objective\n"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import pyorc\n", "import matplotlib.pyplot as plt\n", "import xarray as xr\n", "import cartopy\n", "import cartopy.crs as ccrs\n", "import cartopy.io.img_tiles as cimgt\n", "from dask.diagnostics import ProgressBar\n", "from matplotlib import patches"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["### load our camera configuration\n", "If you didn't do notebook 01, first go through that notebook to understand how a camera configuration is made.\n", "\n", "Below, the camera configuration is loaded back into memory, and used to open a video file. We only need a couple of seconds video, so we use frame 0 until frame 125 only. We set `h_a` at zero, whioch is the same level as `h_ref` used in the camera configuration. This is because we use the same video. If we would use a video at a later moment in which the water level is 15 cm higher for instance, we would have to set `h_a=0.15` instead.\n", "\n", "Because the video is taken with a smartphone (sorry, my hand is not super steady :-)) we also introduce the ``stabilize`` parameter. If you provide a list of pixel coordinates like ``[[x1, y1], [x2, y2], ...]`` that together form a polygon around the water surface, then any area outside of this polygon will be assumed to be stable. If any movements are detected in this area, these can then be assumed to be related to slight shaking of hands, and are used to correct video movements."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["cam_config = pyorc.load_camera_config(\"ngwerere/ngwerere.json\")\n", "video_file = \"ngwerere/ngwerere_20191103.mp4\"\n", "# set coordinates that encapsulate the water surface at minimum. Areas outside will be used for stabilization\n", "stabilize = [\n", "    [150, 0],\n", "    [500, 1079],\n", "    [1750, 1079],\n", "    [900, 0]\n", "]\n", "video = pyorc.Video(\n", "    video_file,\n", "    camera_config=cam_config,\n", "    start_frame=0,\n", "    end_frame=125,\n", "    stabilize=stabilize,\n", "    h_a=0.,\n", ")\n", "video\n"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["### Extract one frame with stabilization window\n", "Below, let's have a look how the stabilization window encompasses the water surface. By including this, any area outside of the water is assumed (in the real world) to be stable and not moving, and any movements found there can then be corrected."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# some keyword arguments for fancy polygon plotting\n", "patch_kwargs = {\n", "    \"alpha\": 0.5,\n", "    \"zorder\": 2,\n", "    \"edgecolor\": \"w\",\n", "    \"label\": \"Area of interest\",\n", "}\n", "f, ax = plt.subplots(1, 1, figsize=(10, 6))\n", "\n", "frame = video.get_frame(0, method=\"rgb\")\n", "# plot frame on a notebook-style window\n", "ax.imshow(frame)\n", "# add the polygon to the axes\n", "patch = patches.Polygon(\n", "    stabilize,\n", "    **patch_kwargs\n", ")\n", "p = ax.add_patch(patch)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["### extracting gray scaled frames\n", "You can see the video holds information about the video itself (filename, fps, and so on) but also the camera configuration supplied to it. We can now extract the frames. Without any arguments, the frames are automatically grayscaled and all frames are extracted."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["da = video.get_frames()\n", "da"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["The frames object is really a `xarray.DataFrame` object, with some additional functionalities under the method `.frames`. The beauty of our API is that it also uses lazy dask arrays to prevent very lengthy runs that then result in gibberish because of a small mistake along the way. We can see the shape and datatype of the end result, without actually computing everything, until we request a sample. Let's have a look at only the first frame with the plotting functionalities. If you want to use the default plot functionalities of `xarray` simply replace the line below by:\n", "```\n", "da[0].plot(cmap=\"gray\")\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["da[0].frames.plot(cmap=\"gray\")\n"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### normalize to add contrast\n", "the `.frames` methods hold functionalities to improve the contrast of the image. A very good step is to remove the average of a larger set of frames from the frames itself, so that only strongly contrasting patterns from the background are left over. These are better traceable. We do this with the `.normalize` method. By default, 15 samples are used."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["# da_norm = da.frames.time_diff(abs=False, thres=0.)\n", "# da_norm = da_norm.frames.minmax(min=0.)\n", "da_norm = da.frames.normalize()\n", "p = da_norm[0].frames.plot(cmap=\"gray\")\n", "plt.colorbar(p)\n"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["A lot more contrast is visible now. We can now project the frames to an orthoprojected plane. The camera configuration, which is part of the `Video` object is used under the hood to do this. We use the new numpy-based projection method. The default is to use OpenCV methods, remove `method=\"numpy\"` to try that."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["f = plt.figure(figsize=(16, 9))\n", "da_norm_proj = da_norm.frames.project(method=\"numpy\")\n", "da_norm_proj[0].frames.plot(cmap=\"gray\")\n"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["You can see that the frames now also have x and y coordinates. These are in fact geographically aware, because we measured control points in real world coordinates and added a coordinate reference system to the `CameraConfig` object (see notebook 01). The `DataArray` therefore also contains coordinate grids for `lon` and `lat` for longitudes and latitudes. Hence we can also go through this entire pipeline with an RGB image and plot this in the real world by adding `mode=\"geographical\"` to the plotting functionalities. The grid is rotated so that its orientation always can follow the stream (in the local projection shown above, left is upstream, right downstream). \n", "Plotting of an rgb frame geographically is done as follows:\n"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# extract frames again, but now with rgb\n", "da_rgb = video.get_frames(method=\"rgb\")\n", "# project the rgb frames, same as before\n", "da_rgb_proj = da_rgb.frames.project()\n", "# plot the first frame in geographical mode\n", "p = da_rgb_proj[0].frames.plot(mode=\"geographical\")\n", "\n", "# for fun, let's also add a satellite background from cartopy\n", "import cartopy.io.img_tiles as cimgt\n", "import cartopy.crs as ccrs\n", "tiles = cimgt.GoogleTiles(style=\"satellite\")\n", "p.axes.add_image(tiles, 19)\n", "# zoom out a little bit so that we can actually see a bit\n", "p.axes.set_extent([\n", "    da_rgb_proj.lon.min() - 0.0001,\n", "    da_rgb_proj.lon.max() + 0.0001,\n", "    da_rgb_proj.lat.min() - 0.0001,\n", "    da_rgb_proj.lat.max() + 0.0001],\n", "    crs=ccrs.<PERSON><PERSON>()\n", ")\n"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["### Velocimetry estimates\n", "Now that we have real-world projected frames, with contrast enhanced, let's do some velocimetry! For Particle Image Velocimetry, this is as simple as calling the `.get_piv` method on the frames. We then store the result in a nice NetCDF file. This file can be loaded back into memory with the `xarray` API without any additional fuss. We also use our new `numba` engine to compute PIV (version >= 0.7.0). This is faster than the original `openpiv` engine. If you want to try `openpiv` you can do this by changing the engine.\n", "```python\n", "piv = da_norm_proj.frames.get_piv(engine=\"openpiv\")\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["piv = da_norm_proj.frames.get_piv(engine=\"numba\")\n", "piv.to_netcdf(\"ngwerere_piv.nc\")\n"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["### Beautiful additions to your art gallery\n", "Of course now we want to do some plotting and filtering, for that, please go to the next notebook"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}