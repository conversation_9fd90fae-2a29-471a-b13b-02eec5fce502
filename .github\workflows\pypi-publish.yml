# This workflow will upload a Python Package using Twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Upload Python Package to PyPI

on:
  # only allow running the workflow manually! This has to be right.
  workflow_dispatch:

permissions:
  contents: read

jobs:
  # This workflow contains a single job called "deploy"
  deploy:
    # type of runner the job will run on
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: OpenCV dependencies
      run: |
        sudo apt update
        sudo apt install libegl1 libopengl0 ffmpeg -y
        
    - name: Install pip dependencies
      run: |
        python -m pip install --upgrade pip
        pip install tomli flit twine
        
    - name: Build package
      run: |
        git clean -xdf
        git restore -SW .
        flit build
        python -m twine check dist/*
      
    - name: Publish package to PyPI
      uses: pypa/gh-action-pypi-publish@v1.12.4
      with:
        user: __token__
        password: ${{ secrets.PYPI_TOKEN }}
        verbose: true
        skip_existing: true
